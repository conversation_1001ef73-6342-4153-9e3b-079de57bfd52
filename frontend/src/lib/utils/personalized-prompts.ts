/**
 * Personalized Prompt System
 *
 * Defines the initial prompts and their integration requirements for the
 * personalized prompt recommendation system.
 */

import { IntegrationRequirement } from './integration-checker';

export interface PersonalizedPrompt {
  id: string;
  title: string;
  query: string; // Template with {user.email} placeholder
  description: string;
  integrations: IntegrationRequirement[];
  category: 'initial' | 'recommended';
  priority: number; // Higher number = higher priority
}

/**
 * The 4 initial prompts shown to users with no custom MCPs configured
 */
export const INITIAL_PROMPTS: PersonalizedPrompt[] = [
  {
    id: 'startup-founders-research',
    title: 'Funded Startups Hiring Research',
    query: 'Find 10 startup founders in YC who would be looking to hire engineering talent and make a CSV file with their info, ask me to connect gmail on the /apps page so i can email this file once you complete the intiial task',
    description: 'Research Y Combinator startup founders seeking engineering talent',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'FindAnyone',
        required: true,
      },
    ],
    category: 'initial',
    priority: 4,
  },
  {
    id: 'welcome-email',
    title: 'Welcome to Atlas!',
    query: 'send {user.email} a beautiful personalized welcome email to the atlasagents.ai. Mention some of our cool use cases in the email.',
    description: 'Send yourself a personalized welcome email showcasing Atlas capabilities',
    integrations: [
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'initial',
    priority: 3,
  },
  {
    id: 'law-firms-research',
    title: 'Lead Research & Outreach',
    query: 'find 20 mid-sized law firms in nyc, find an employee from each first, populate a google sheet with their info and email the link to the sheet to {user.email}',
    description: 'Research NYC law firms, gather contact info, and organize in a spreadsheet',
    integrations: [
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'FindAnyone',
        required: true,
      },
    ],
    category: 'initial',
    priority: 2,
  },
  {
    id: 'calendar-email-prep',
    title: 'Upcoming Week Meeting Prep',
    query: 'check my calendar events on g cal for next weeks events, get the attendee emails and create drafts in gmail with heads-up emails to each of them so and get them ready for me to review',
    description: 'Prepare follow-up emails for upcoming calendar events',
    integrations: [
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
      {
        app_key: 'google_calendar',
        name: 'Google Calendar',
        required: true,
      },
    ],
    category: 'initial',
    priority: 1,
  },
];

/**
 * Advanced prompts shown to users based on their connected integrations
 */
export const ADVANCED_PROMPTS: PersonalizedPrompt[] = [
  // Sales & Marketing Automation
  {
    id: 'lead-generation-pipeline',
    title: 'End-to-End Lead Generation Pipeline',
    query: 'Find 20 VP Marketing contacts at Series A SaaS companies, enrich their data with FindAnyone, create a tracking sheet in Google Sheets, and draft personalized cold emails using our Notion messaging templates.',
    description: 'Complete lead generation workflow with data enrichment and email outreach',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'FindAnyone',
        required: true,
      },
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
      {
        app_key: 'notion',
        name: 'Notion',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 10,
  },
  {
    id: 'social-media-intelligence',
    title: 'Social Media Intelligence & Response',
    query: 'Monitor Twitter mentions of our competitors, summarize sentiment in a Notion page, and draft response tweets for our brand account.',
    description: 'Track competitor mentions and automate social media responses',
    integrations: [
      {
        app_key: 'twitter',
        name: 'Twitter',
        required: true,
      },
      {
        app_key: 'notion',
        name: 'Notion',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 8,
  },
  {
    id: 'hubspot-sales-automation',
    title: 'HubSpot Sales Sequence Automation',
    query: 'When a new lead fills out our contact form, create a HubSpot contact, add them to our nurture sequence, and notify the sales team via Gmail with their enriched profile.',
    description: 'Automate lead capture and sales team notifications',
    integrations: [
      {
        app_key: 'hubspot',
        name: 'HubSpot',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'FindAnyone',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 9,
  },

  // Product & Project Management
  {
    id: 'cross-platform-project-sync',
    title: 'Cross-Platform Project Sync',
    query: 'Sync our Linear sprint progress with ClickUp tasks, update our product roadmap in Notion, and send a weekly status email to stakeholders.',
    description: 'Keep all project management tools in sync with automated reporting',
    integrations: [
      {
        app_key: 'linear',
        name: 'Linear',
        required: true,
      },
      {
        app_key: 'clickup',
        name: 'ClickUp',
        required: true,
      },
      {
        app_key: 'notion',
        name: 'Notion',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 7,
  },
  {
    id: 'customer-feedback-analysis',
    title: 'Customer Feedback Analysis',
    query: 'Analyze all HubSpot support tickets tagged "feature request" from this month, identify patterns, and create prioritized Linear epics with supporting data.',
    description: 'Turn customer feedback into actionable product roadmap items',
    integrations: [
      {
        app_key: 'hubspot',
        name: 'HubSpot',
        required: true,
      },
      {
        app_key: 'notion',
        name: 'Notion',
        required: true,
      },
      {
        app_key: 'linear',
        name: 'Linear',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 6,
  },

  // Operations & Productivity
  {
    id: 'meeting-automation',
    title: 'Meeting Automation & Follow-up',
    query: 'Schedule a Zoom meeting for next week, add it to Google Calendar, create a Notion agenda template, and send calendar invites via Gmail with the agenda attached.',
    description: 'Automate meeting setup with agenda creation and invitations',
    integrations: [
      {
        app_key: 'zoom',
        name: 'Zoom',
        required: true,
      },
      {
        app_key: 'google_calendar',
        name: 'Google Calendar',
        required: true,
      },
      {
        app_key: 'notion',
        name: 'Notion',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 8,
  },
  {
    id: 'document-collaboration',
    title: 'Document Collaboration Workflow',
    query: 'Create a new Google Doc for our project proposal, share it with the team via Slack, track changes in Notion, and send weekly progress updates via Gmail.',
    description: 'Streamline document creation and collaboration across teams',
    integrations: [
      {
        app_key: 'google_docs',
        name: 'Google Docs',
        required: true,
      },
      {
        app_key: 'slack',
        name: 'Slack',
        required: true,
      },
      {
        app_key: 'notion',
        name: 'Notion',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 7,
  },
  {
    id: 'team-communication-sync',
    title: 'Team Communication Sync',
    query: 'Monitor important Slack channels, summarize key discussions in Notion, create action items in Linear, and send daily team updates via Gmail.',
    description: 'Keep team aligned with automated communication summaries',
    integrations: [
      {
        app_key: 'slack',
        name: 'Slack',
        required: true,
      },
      {
        app_key: 'notion',
        name: 'Notion',
        required: true,
      },
      {
        app_key: 'linear',
        name: 'Linear',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 6,
  },

  // CRM & Sales
  {
    id: 'salesforce-pipeline-management',
    title: 'Salesforce Pipeline Management',
    query: 'Update Salesforce opportunities based on recent email interactions, create follow-up tasks in ClickUp, and send personalized outreach emails via Gmail.',
    description: 'Automate sales pipeline updates and follow-up actions',
    integrations: [
      {
        app_key: 'salesforce',
        name: 'Salesforce',
        required: true,
      },
      {
        app_key: 'clickup',
        name: 'ClickUp',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 8,
  },
  {
    id: 'crm-data-enrichment',
    title: 'CRM Data Enrichment',
    query: 'Enrich all new HubSpot contacts with FindAnyone data, update their profiles, create personalized outreach sequences, and track engagement in Google Sheets.',
    description: 'Automatically enrich and organize CRM contact data',
    integrations: [
      {
        app_key: 'hubspot',
        name: 'HubSpot',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'FindAnyone',
        required: true,
      },
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 9,
  },

  // Content & Social Media
  {
    id: 'content-distribution',
    title: 'Content Distribution Pipeline',
    query: 'Create a blog post in Google Docs, publish it to our website, share on Twitter with engaging copy, and track performance in Airtable.',
    description: 'Automate content creation and multi-platform distribution',
    integrations: [
      {
        app_key: 'google_docs',
        name: 'Google Docs',
        required: true,
      },
      {
        app_key: 'twitter',
        name: 'Twitter',
        required: true,
      },
      {
        app_key: 'airtable',
        name: 'Airtable',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 5,
  },
  {
    id: 'social-media-calendar',
    title: 'Social Media Calendar Management',
    query: 'Plan next month\'s Twitter content in Airtable, schedule posts, track engagement metrics, and create performance reports in Google Sheets.',
    description: 'Organize and automate social media content planning',
    integrations: [
      {
        app_key: 'airtable',
        name: 'Airtable',
        required: true,
      },
      {
        app_key: 'twitter',
        name: 'Twitter',
        required: true,
      },
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 4,
  },
  // Legal Research & Outreach
  {
    id: 'nyc-law-firms-research',
    title: 'NYC Law Firms Research & Outreach',
    query: 'Find 20 mid-sized law firms in NYC, find an employee from each firm, populate a Google Sheet with their info and email the link to the <NAME_EMAIL>',
    description: 'Research law firms, compile employee data, and share via email',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'FindAnyone',
        required: true,
      },
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 8,
  },
  {
    id: 'calendar-meeting-reminders',
    title: 'Meeting Reminder Automation',
    query: 'Check my calendar events on Google Calendar for next week\'s events, get the attendee emails and create professional drafts in Gmail with heads-up emails to each of them to remind them to not forget the meeting.',
    description: 'Automate meeting reminders for calendar attendees',
    integrations: [
      {
        app_key: 'google_calendar',
        name: 'Google Calendar',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 7,
  },
  {
    id: 'bmw-i8-pricing-model',
    title: 'BMW i8 Market Analysis & Pricing Model',
    query: 'Do research on BMW i8s and create a model based on the data to compute the fair market price of a car if I give you a mileage and a year.',
    description: 'Create data-driven pricing model for BMW i8 vehicles',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 6,
  },
  {
    id: 'b2b-saas-investment-analysis',
    title: 'B2B SaaS Investment Analysis',
    query: 'Analyze 5 B2B SaaS companies for potential investment by researching their financials and market position, create Google Sheets valuation models, generate due diligence reports in Google Docs',
    description: 'Comprehensive investment analysis with financial modeling',
    integrations: [
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
      {
        app_key: 'google_docs',
        name: 'Google Docs',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 8,
  },
  {
    id: 'max-styler-seo-blog',
    title: 'SEO Blog Post Creation',
    query: 'Research and create a comprehensive, SEO-optimized blog post on "How to Book Max Styler for Your Private Party". Create a 1,500+ word SEO-optimized HTML article to drive qualified leads for a talent booking business.',
    description: 'Create SEO-optimized content for talent booking business',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 5,
  },
  {
    id: 'saas-financial-modeling',
    title: 'SaaS Financial Modeling & Valuation',
    query: 'Gather historical financial data on publicly traded SaaS companies, including revenue, EBITDA, growth rate, and valuation multiples (EV/Revenue, EV/EBITDA). Then build a regression model to estimate a company\'s fair valuation based on its current financials.',
    description: 'Build regression model for SaaS company valuations',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 7,
  },
  {
    id: 'personal-finance-app-research',
    title: 'Personal Finance App Market Research',
    query: 'Research the personal finance app market including competitors like Mint and YNAB, create Google Sheets competitive analysis and market sizing, develop a comprehensive go-to-market strategy in Google Docs, then email the <NAME_EMAIL>',
    description: 'Complete market research and strategy for fintech app',
    integrations: [
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
      {
        app_key: 'google_docs',
        name: 'Google Docs',
        required: true,
      },
      {
        app_key: 'gmail',
        name: 'Gmail',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 8,
  },
  {
    id: 'web3-influencer-research',
    title: 'Web3 Influencer Research',
    query: 'Find 10 web3 influencers on X with active engagement and under 50k followers. Compile a comprehensive list of web3/crypto influencers we could approach for paid advertisement for our agentic automation company.',
    description: 'Identify and analyze web3 influencers for marketing',
    integrations: [
      {
        app_key: 'twitter',
        name: 'Twitter',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 6,
  },
  {
    id: 'project-management-reddit-research',
    title: 'Project Management Tool Market Research',
    query: 'Conduct market research for a new project management tool by analyzing user pain points and feature requests in relevant subreddits.',
    description: 'Analyze Reddit for project management user insights',
    integrations: [
      {
        app_key: 'reddit',
        name: 'Reddit',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 5,
  },
  {
    id: 'male-grooming-competitor-analysis',
    title: 'Male Grooming Industry Analysis',
    query: 'Conduct competitor analysis of brands in the US in the male-grooming industry. Create a Google spreadsheet with 10 such competitors and include relevant information like their target customer, their best-selling products and what their specific social media strategy currently is',
    description: 'Comprehensive competitor analysis for grooming industry',
    integrations: [
      {
        app_key: 'google_sheets',
        name: 'Google Sheets',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 6,
  },
  {
    id: 'tequila-industry-india-analysis',
    title: 'Tequila Industry Analysis - India',
    query: 'Conduct trend analysis of the tequila industry in India (find statistics to support your claims). Find a comprehensive list of alcohol distributors in India for tequila specifically. Find statistics that support the opportunity for a new tequila distributor in India.',
    description: 'Market opportunity analysis for tequila distribution in India',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 6,
  },
  {
    id: 'financial-news-twitter',
    title: 'Financial News Twitter Updates',
    query: 'Browse the WSJ and/or other financial news source and generate a tweet about the current affairs',
    description: 'Create social media content from financial news',
    integrations: [
      {
        app_key: 'twitter',
        name: 'Twitter',
        required: true,
      },
      {
        app_key: 'findanyone',
        name: 'Web Search Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 4,
  },
  {
    id: 'skincare-app-analysis',
    title: 'Skincare App Competitive Analysis',
    query: 'Analyze my app idea and create a comprehensive list of similar apps. App idea: A skin care app that analyzes what each product in your regimen and guides the user in terms of when and how to apply it (AM/PM routine builder), flags important warnings (don\'t mix benzoyl peroxide + tretinoin, etc.), finds the exact product online and gives a buy link (via Tata 1mg, NetMeds, Amazon, etc.), suggests cheaper or gentler alternatives if available',
    description: 'Competitive analysis for skincare app concept',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 5,
  },
  {
    id: 'daily-news-summary',
    title: 'Daily News Summary',
    query: 'Review today\'s headlines across popular newspapers and write a short-form summary for me to read',
    description: 'Create personalized daily news digest',
    integrations: [
      {
        app_key: 'findanyone',
        name: 'Research Tools',
        required: true,
      },
    ],
    category: 'recommended',
    priority: 3,
  },
];

/**
 * Replace {user.email} placeholder in prompt query with actual user email
 */
export function personalizePromptQuery(query: string, userEmail: string): string {
  return query.replace(/{user\.email}/g, userEmail);
}

/**
 * Get prompts that user can run based on their connected integrations
 */
export function getAvailablePrompts(
  prompts: PersonalizedPrompt[],
  userIntegrations: { [key: string]: boolean }
): PersonalizedPrompt[] {
  return prompts.filter(prompt => {
    return prompt.integrations.every(integration => {
      if (!integration.required) return true;
      return userIntegrations[integration.app_key] === true;
    });
  });
}

/**
 * Get prompts that user cannot run (missing integrations)
 */
export function getUnavailablePrompts(
  prompts: PersonalizedPrompt[],
  userIntegrations: { [key: string]: boolean }
): PersonalizedPrompt[] {
  return prompts.filter(prompt => {
    return prompt.integrations.some(integration => {
      if (!integration.required) return false;
      return userIntegrations[integration.app_key] !== true;
    });
  });
}

/**
 * Get missing integrations for a specific prompt
 */
export function getMissingIntegrationsForPrompt(
  prompt: PersonalizedPrompt,
  userIntegrations: any
): IntegrationRequirement[] {
  return prompt.integrations.filter(integration => {
    if (!integration.required) return false;
    return userIntegrations[integration.app_key] !== true;
  });
}

/**
 * Sort prompts by priority (higher priority first)
 */
export function sortPromptsByPriority(prompts: PersonalizedPrompt[]): PersonalizedPrompt[] {
  return [...prompts].sort((a, b) => b.priority - a.priority);
}
