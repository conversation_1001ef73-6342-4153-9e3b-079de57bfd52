'use client';

import React, { useEffect, useState, useMemo } from 'react';
import {
  Plus,
  Shield,
  Loader2,
  AlertTriangle,
  Settings2,
  Sparkles,
  Clock,
  Server,
  Globe,
  Zap,
  MoreHorizontal,
  ChevronDown,
  ChevronRight,
  Menu,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toast } from 'sonner';
import { useAllAgentMCPs, useUpdateAgentCustomMCPs } from '@/hooks/react-query/agents/use-agents';
import { MCPJsonEditorDialog } from './_components/mcp-json-editor-dialog';
import { getComposioAppIcon } from '@/lib/icon-mapping';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';
import { useFeatureFlag } from '@/lib/feature-flags';
import { ComposioMCPService } from '@/lib/composio-api';
import { ComposioApp } from '@/types/composio';
import { createClient } from '@/lib/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';

export default function CredentialsPage() {
  const { enabled: customAgentsEnabled, loading: flagLoading } = useFeatureFlag("custom_agents");
  const router = useRouter();
  const queryClient = useQueryClient();
  const isMobile = useIsMobile();
  const { setOpenMobile, openMobile } = useSidebar();

  useEffect(() => {
    if (!flagLoading && !customAgentsEnabled) {
      router.replace("/dashboard");
    }
  }, [flagLoading, customAgentsEnabled, router]);

  const [showMCPEditor, setShowMCPEditor] = useState(false);
  const [editingMCP, setEditingMCP] = useState<{
    mcp: any;
    agentId: string;
    agentName: string;
    mcpIndex: number;
  } | null>(null);

  // Available apps state (from Composio)
  const [availableApps, setAvailableApps] = useState<ComposioApp[]>([]);
  const [isLoadingAvailableApps, setIsLoadingAvailableApps] = useState(true);
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());

  const { data: allAgentMCPs, isLoading: isLoadingAgentMCPs, refetch: refetchAgentMCPs } = useAllAgentMCPs();
  const updateAgentCustomMCPsMutation = useUpdateAgentCustomMCPs();

  // Helper function to get authenticated headers
  const getAuthHeaders = async () => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No authentication token available. Please sign in.');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  };

  // Start polling for connection status
  const startConnectionPolling = React.useCallback((appKey: string, appName: string) => {
    let attempts = 0;
    const maxAttempts = 20; // 2 minutes max

    const pollStatus = async () => {
      attempts++;

      try {
        const connectionRequestId = localStorage.getItem('composio_connection_request_id');
        if (!connectionRequestId) return;

        const statusResult = await ComposioMCPService.checkConnectionStatus(
          connectionRequestId,
          appKey as any
        );

        if (statusResult.success && statusResult.is_connected) {
          // Success! Clear storage and update UI
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');
          localStorage.removeItem('composio_connection_app_name');

          // Update connected apps
          setConnectedApps(prev => new Set(prev).add(appKey));

          // Invalidate React Query cache and refresh agent MCPs
          queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
          queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
          await refetchAgentMCPs(); // Refresh agent MCPs to show newly connected servers

          toast.success('Authentication Complete!', {
            description: `${appName} is now connected and ready to use.`
          });

          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(pollStatus, 6000); // Poll every 6 seconds
        }
      } catch (error) {
        console.error('Error checking connection status:', error);
        if (attempts < maxAttempts) {
          setTimeout(pollStatus, 6000);
        }
      }
    };

    setTimeout(pollStatus, 2000); // Start polling after 2 seconds
  }, [queryClient]);

  // Load available apps from Composio
  useEffect(() => {
    const loadAvailableApps = async () => {
      try {
        setIsLoadingAvailableApps(true);
        const data = await ComposioMCPService.getSupportedApps();

        if (data.success) {
          setAvailableApps(data.apps);
        } else {
          console.error('Failed to load supported apps:', data.message);
        }
      } catch (error) {
        console.error('Error loading available apps:', error);
      } finally {
        setIsLoadingAvailableApps(false);
      }
    };

    loadAvailableApps();
  }, []);

  // Load connected apps from Composio
  useEffect(() => {
    const loadConnectedApps = async () => {
      try {
        const connections = await ComposioMCPService.listUserConnections();
        const connectedAppKeys = new Set(connections.map(conn => conn.app_key));
        setConnectedApps(connectedAppKeys);
      } catch (error) {
        console.error('Error loading connected apps:', error);
      }
    };

    const handlePostOAuthRefresh = async () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      const connectionRequestId = localStorage.getItem('composio_connection_request_id');

      if (recentlyConnectedKey && connectionRequestId) {
        console.log(`Checking if ${recentlyConnectedKey} connection completed...`);

        try {
          const statusResult = await ComposioMCPService.checkConnectionStatus(
            connectionRequestId,
            recentlyConnectedKey as any
          );

          if (statusResult.success && statusResult.is_connected) {
            // Clear flags and update state
            localStorage.removeItem('composio_recently_connected');
            localStorage.removeItem('composio_connection_request_id');
            localStorage.removeItem('composio_connection_app_name');

            // Reload connections to show updated state
            await loadConnectedApps();

            // Invalidate React Query cache and refresh agent MCPs
            queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
            queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
            await refetchAgentMCPs(); // Refresh agent MCPs to show newly connected servers

            toast.success('Authentication Complete!', {
              description: `${recentlyConnectedKey} is now connected and ready to use.`
            });
          } else {
            // Not connected yet, start polling
            const appName = localStorage.getItem('composio_connection_app_name') || recentlyConnectedKey;
            startConnectionPolling(recentlyConnectedKey, appName);
          }
        } catch (error) {
          console.error('Error checking post-OAuth status:', error);
          const appName = localStorage.getItem('composio_connection_app_name') || recentlyConnectedKey;
          startConnectionPolling(recentlyConnectedKey, appName);
        }
      }
    };

    const handleWindowFocus = () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      if (recentlyConnectedKey) {
        handlePostOAuthRefresh();
      }
    };

    // Load connections and check for post-OAuth refresh
    loadConnectedApps();
    handlePostOAuthRefresh();

    // Listen for window focus
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [queryClient, startConnectionPolling]);

  // Handle connection to available apps
  const handleConnectApp = async (appKey: string, appName: string) => {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

    setConnectingApps(prev => new Set(prev).add(appKey));
    toast('Authenticating...', { description: `Connecting to ${appName}` });

    try {
      // Step 1: Initiate connection and get redirect URL
      const initResult = await ComposioMCPService.initiateConnection(appKey);

      // Step 2: Store flags for post-OAuth handling
      localStorage.setItem('composio_recently_connected', appKey);
      localStorage.setItem('composio_connection_request_id', initResult.connection_request_id);
      localStorage.setItem('composio_connection_app_name', appName);

      // Step 3: Use full page redirect for all browsers to avoid cross-origin issues
      window.location.href = initResult.redirect_url;

      // Step 4: Start polling for connection status
      startConnectionPolling(appKey, appName);

    } catch (error: any) {
      console.error('Connection error:', error);
      toast.error('Connection Failed', {
        description: error.message || `Failed to connect to ${appName}`
      });
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  };

  // Handle disconnection of apps
  const handleDisconnectApp = async (appKey: string, appName: string) => {
    if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return;

    setDisconnectingApps(prev => new Set(prev).add(appKey));

    try {
      // Optimistic update - remove from UI immediately
      setConnectedApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });

      // Call the disconnect API
      const success = await ComposioMCPService.deleteConnection(appKey);

      if (success) {
        // Invalidate React Query cache and refresh agent MCPs
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
        await refetchAgentMCPs(); // Refresh to remove disconnected servers from MCP list

        toast.success('Disconnected Successfully', {
          description: `${appName} has been disconnected.`
        });
      } else {
        // Rollback optimistic update on failure
        setConnectedApps(prev => new Set(prev).add(appKey));
        toast.error('Disconnection Failed', {
          description: `Failed to disconnect ${appName}. Please try again.`
        });
      }
    } catch (error: any) {
      console.error('Disconnect error:', error);
      // Rollback optimistic update on error
      setConnectedApps(prev => new Set(prev).add(appKey));
      toast.error('Disconnection Failed', {
        description: error.message || `Failed to disconnect ${appName}`
      });
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  };

  const handleSaveMCP = async (agentId: string, mcpIndex: number, updatedMcp: any) => {
    try {
      // Find the agent and get its current custom_mcps
      const agent = allAgentMCPs?.agents.find(a => a.agent_id === agentId);
      if (!agent) {
        throw new Error('Agent not found');
      }

      // Create a copy of the current custom_mcps and update the specific MCP
      const updatedCustomMcps = [...agent.custom_mcps];
      updatedCustomMcps[mcpIndex] = updatedMcp;

      // Update the agent's custom_mcps
      await updateAgentCustomMCPsMutation.mutateAsync({
        agentId,
        customMcps: updatedCustomMcps
      });

      // Refresh the agent MCPs data
      await refetchAgentMCPs();
    } catch (error) {
      console.error('Error saving MCP:', error);
      throw error;
    }
  };

  // Combine all MCPs across agents with duplicate handling
  const combinedMCPs = useMemo(() => {
    if (!allAgentMCPs?.agents) return [];

    const mcpMap = new Map();

    allAgentMCPs.agents.forEach(agent => {
      agent.custom_mcps.forEach((mcp, index) => {
        const key = `${mcp.name}-${mcp.type}`;
        if (!mcpMap.has(key)) {
          mcpMap.set(key, {
            ...mcp,
            agents: [{
              agent_id: agent.agent_id,
              agent_name: agent.agent_name,
              is_default: agent.is_default,
              mcp_index: index
            }]
          });
        } else {
          // Add this agent to the existing MCP
          mcpMap.get(key).agents.push({
            agent_id: agent.agent_id,
            agent_name: agent.agent_name,
            is_default: agent.is_default,
            mcp_index: index
          });
        }
      });
    });

    return Array.from(mcpMap.values());
  }, [allAgentMCPs]);

  // Combine available apps with connected status, filtering out duplicates
  const combinedApps = useMemo(() => {
    const apps = [...availableApps];

    // Get app names from connected MCPs to avoid duplicates
    const connectedMCPNames = new Set(
      combinedMCPs.map(mcp => mcp.app_name || mcp.name)
    );

    // Filter out apps that are already shown in connected MCPs
    const filteredApps = apps.filter(app => {
      // Check if this app is already shown in connected MCPs
      const appNameVariants = [
        app.name,
        app.name.toLowerCase(),
        app.key,
        app.key.toLowerCase().replace(/_/g, ' ')
      ];

      return !appNameVariants.some(variant =>
        connectedMCPNames.has(variant) ||
        Array.from(connectedMCPNames).some(mcpName =>
          mcpName.toLowerCase().includes(variant.toLowerCase()) ||
          variant.toLowerCase().includes(mcpName.toLowerCase())
        )
      );
    });

    // Sort by connection status (connected first) then by name
    return filteredApps.sort((a, b) => {
      const aConnected = connectedApps.has(a.key);
      const bConnected = connectedApps.has(b.key);

      if (aConnected && !bConnected) return -1;
      if (!aConnected && bConnected) return 1;

      return a.name.localeCompare(b.name);
    });
  }, [availableApps, connectedApps, combinedMCPs]);

  const handleEditCombinedMCP = (combinedMcp: any, agentInfo: any) => {
    setEditingMCP({
      mcp: combinedMcp,
      agentId: agentInfo.agent_id,
      agentName: agentInfo.agent_name,
      mcpIndex: agentInfo.mcp_index
    });
    setShowMCPEditor(true);
  };

  if (flagLoading) {
    return (
      <div className="h-screen max-w-7xl mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
              <Shield className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-foreground">Manage your Apps</h1>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="p-2 bg-neutral-100 dark:bg-sidebar rounded-2xl overflow-hidden group">
              <div className="h-24 flex items-center justify-center relative bg-gradient-to-br from-opacity-90 to-opacity-100">
                <Skeleton className="h-24 w-full rounded-xl" />
              </div>
              <div className="space-y-2 mt-4 mb-4">
                <Skeleton className="h-6 w-32 rounded" />
                <Skeleton className="h-4 w-24 rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!customAgentsEnabled) {
    return null;
  }

  return (
    <>
      {/* Mobile Header */}
      {isMobile && (
        <div className="bg-background border-b border-border px-4 py-3 flex items-center justify-between md:hidden">
          <div className="flex items-center gap-2">
            <Server className="h-5 w-5 text-primary" />
            <h1 className="text-lg font-semibold text-foreground">App Configurations</h1>

          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setOpenMobile(!openMobile)}
              >
                {openMobile ? (
                  <X className="h-4 w-4" />
                ) : (
                  <Menu className="h-4 w-4" />
                )}
                <span className="sr-only">
                  {openMobile ? "Close menu" : "Open menu"}
                </span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {openMobile ? "Close menu" : "Open menu"}
            </TooltipContent>
          </Tooltip>
        </div>
      )}

      <div className="container mx-auto max-w-6xl px-6 py-6">
        <div className="space-y-6">
          {/* MCP Server Configurations Section */}
          <div className="space-y-6">
            <div className='w-full space-y-4 bg-gradient-to-b from-primary/10 to-primary/5 border rounded-xl h-60 flex items-center justify-center'>
              <div className="space-y-4">
                <div className="space-y-2 text-center">
                  <div className='flex items-center justify-center gap-2'>
                    <Server className='h-6 w-6 text-primary' />
                    <h1 className="text-2xl font-semibold tracking-tight text-foreground">
                      App Configurations
                    </h1>
                  </div>
                  <p className="text-md text-muted-foreground max-w-2xl">
                    View and edit app configurations from all your agents
                  </p>
                </div>
              </div>
            </div>

            {isLoadingAgentMCPs || isLoadingAvailableApps ? (
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden">
                    <div className='p-4'>
                      <Skeleton className="h-12 w-12 rounded-lg" />
                    </div>
                    <div className="p-4">
                      <div className="space-y-2">
                        <Skeleton className="h-5 w-24 rounded" />
                        <Skeleton className="h-4 w-full rounded" />
                        <Skeleton className="h-4 w-3/4 rounded" />
                        <div className="flex gap-1 mt-2">
                          <Skeleton className="h-5 w-12 rounded" />
                          <Skeleton className="h-5 w-16 rounded" />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : !allAgentMCPs || (allAgentMCPs.total_custom_mcps === 0 && combinedApps.length === 0) ? (
              <Card className="border-dashed border-border/60 bg-muted/20">
                <CardContent className="p-8 text-center">
                  <div className="space-y-4">
                    <div className="p-3 rounded-full bg-muted/60 w-fit mx-auto">
                      <Server className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <div className="space-y-1">
                      <h3 className="font-semibold text-foreground">No apps available</h3>
                      <p className="text-sm text-muted-foreground">
                        Loading available integrations...
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {/* Connected MCPs from agents */}
                {combinedMCPs.map((combinedMcp, index) => {
                  // Get app icon using the same mapping as the carousel
                  const getAppIconComponent = () => {
                    // Use the MCP name for icon mapping (this is what contains the app info)
                    const appName = combinedMcp.app_name || combinedMcp.name;

                    return getComposioAppIcon({
                      name: appName,
                      key: appName.toLowerCase().replace(/\s+/g, '_'),
                      icon: undefined
                    });
                  };

                  return (
                    <div
                      key={`connected-${combinedMcp.name}-${index}`}
                      className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden hover:bg-muted/50 transition-all duration-200 cursor-pointer group relative"
                    >
                      {/* Connected Badge with Disconnect on Hover */}
                      <div className="absolute top-2 right-2 z-10 group/badge">
                        <Badge
                          variant="secondary"
                          className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 text-xs group-hover/badge:bg-red-100 group-hover/badge:text-red-800 dark:group-hover/badge:bg-red-900 dark:group-hover/badge:text-red-100 cursor-pointer transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            const appKey = Object.keys(availableApps.find(app => app.name === combinedMcp.name) || {}).length > 0
                              ? availableApps.find(app => app.name === combinedMcp.name)?.key
                              : combinedMcp.name.toLowerCase().replace(/\s+/g, '_');
                            if (appKey) {
                              handleDisconnectApp(appKey, combinedMcp.name);
                            }
                          }}
                        >
                          <span className="group-hover/badge:hidden">Connected</span>
                          <span className="hidden group-hover/badge:inline">Disconnect</span>
                        </Badge>
                      </div>

                      <div className='p-4'>
                        <div className="w-12 h-12 flex items-center justify-center">
                          {(() => {
                            const IconComponent = getAppIconComponent();
                            return <IconComponent className="h-6 w-6 text-muted-foreground" />;
                          })()}
                        </div>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center gap-2">
                          <h3 className="text-foreground font-medium text-lg line-clamp-1 flex-1">
                            {combinedMcp.name}
                          </h3>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                <MoreHorizontal className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {combinedMcp.agents.map((agentInfo: any) => (
                                <DropdownMenuItem
                                  key={agentInfo.agent_id}
                                  onClick={() => handleEditCombinedMCP(combinedMcp, agentInfo)}
                                >
                                  Edit in {agentInfo.agent_name}
                                </DropdownMenuItem>
                              ))}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Available Apps */}
                {combinedApps.map((app, index) => {
                  const isConnected = connectedApps.has(app.key);
                  const isConnecting = connectingApps.has(app.key);
                  const isDisconnecting = disconnectingApps.has(app.key);

                  return (
                    <div
                      key={`app-${app.key}-${index}`}
                      className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden hover:bg-muted/50 transition-all duration-200 cursor-pointer group relative"
                    >
                      {/* Status Badge */}
                      {isConnected ? (
                        <div className="absolute top-2 right-2 z-10 group/badge">
                          <Badge
                            variant="secondary"
                            className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 text-xs group-hover/badge:bg-red-100 group-hover/badge:text-red-800 dark:group-hover/badge:bg-red-900 dark:group-hover/badge:text-red-100 cursor-pointer transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDisconnectApp(app.key, app.name);
                            }}
                          >
                            {isDisconnecting ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              <>
                                <span className="group-hover/badge:hidden">Connected</span>
                                <span className="hidden group-hover/badge:inline">Disconnect</span>
                              </>
                            )}
                          </Badge>
                        </div>
                      ) : (
                        <div className="absolute top-2 right-2 z-10">
                          <Badge
                            variant="outline"
                            className="bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 text-xs"
                          >
                            {isConnecting ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              "Disconnected"
                            )}
                          </Badge>
                        </div>
                      )}

                      {/* Plus icon for disconnected apps - bottom right corner */}
                      {!isConnected && (
                        <div className="absolute bottom-3 right-3 z-10">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleConnectApp(app.key, app.name);
                            }}
                            className="flex items-center justify-center w-8 h-8 bg-white hover:bg-gray-50 dark:bg-white dark:hover:bg-gray-100 rounded-full transition-all duration-300 group/connect shadow-sm border border-gray-200 hover:scale-125 hover:shadow-lg"
                            disabled={isConnecting}
                          >
                            {isConnecting ? (
                              <Loader2 className="h-4 w-4 animate-spin text-gray-600" />
                            ) : (
                              <Plus className="h-4 w-4 text-gray-600 group-hover/connect:scale-125 transition-all duration-300" />
                            )}
                          </button>
                        </div>
                      )}

                      <div className='p-4'>
                        <div className="w-12 h-12 flex items-center justify-center">
                          {(() => {
                            const IconComponent = getComposioAppIcon(app);
                            return <IconComponent className="h-6 w-6 text-muted-foreground" />;
                          })()}
                        </div>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center gap-2">
                          <h3 className="text-foreground font-medium text-lg line-clamp-1 flex-1">
                            {app.name}
                          </h3>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          <MCPJsonEditorDialog
            open={showMCPEditor}
            onOpenChange={setShowMCPEditor}
            mcp={editingMCP?.mcp || null}
            agentId={editingMCP?.agentId || ''}
            agentName={editingMCP?.agentName || ''}
            mcpIndex={editingMCP?.mcpIndex || 0}
            onSave={handleSaveMCP}
          />
        </div>
      </div>
    </>
  );
}
