'use client'

import { useState, useEffect } from 'react'
import { Search, Grid, List, Filter, FileText, Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useIsMobile } from '@/hooks/use-mobile'
import { useSidebar } from '@/components/ui/sidebar'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { AllPrompts } from './_components/all-prompts'
import { ComposioApp } from '@/types/composio'
import { ComposioMCPService } from '@/lib/composio-api'
import { MCPAppsModal } from '@/components/dashboard/mcp-apps-modal'
import { createClient } from '@/lib/supabase/client'
import { useQueryClient } from '@tanstack/react-query'
import { agentKeys } from '@/hooks/react-query/agents/keys'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

export default function PromptsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const isMobile = useIsMobile()
  const { setOpenMobile, openMobile } = useSidebar()
  const router = useRouter()
  const queryClient = useQueryClient()

  // MCP Apps Modal State for filtered integration requirements
  const [apps, setApps] = useState<ComposioApp[]>([])
  const [loading, setLoading] = useState(true)
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set())
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set())
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set())
  const [isFilteredModalOpen, setIsFilteredModalOpen] = useState(false)
  const [filteredAppKeys, setFilteredAppKeys] = useState<string[]>([])
  const [modalTitle, setModalTitle] = useState<string>('')
  const [modalDescription, setModalDescription] = useState<string>('')

  // Load MCP apps for filtered modal
  useEffect(() => {
    const loadApps = async () => {
      try {
        setLoading(true)
        const data = await ComposioMCPService.getSupportedApps()
        if (data.success) {
          setApps(data.apps)
        }
      } catch (error) {
        console.error('Error loading apps:', error)
      } finally {
        setLoading(false)
      }
    }

    loadApps()
  }, [])

  // Load connected apps from Composio
  useEffect(() => {
    const loadConnectedApps = async () => {
      try {
        const connections = await ComposioMCPService.listUserConnections()
        const connectedAppKeys = new Set(connections.map(conn => conn.app_key))
        setConnectedApps(connectedAppKeys)
      } catch (error) {
        console.error('Error loading connected apps:', error)
      }
    }

    loadConnectedApps()
  }, [])

  // Handler for when Examples component needs to show integration requirements
  const handleRequireIntegrations = (appKeys: string[], promptTitle: string) => {
    setFilteredAppKeys(appKeys)
    setModalTitle('Connect Required Apps')
    setModalDescription(`To use "${promptTitle}", you need to connect the following integrations:`)
    setIsFilteredModalOpen(true)
  }

  // Handle MCP server connection
  const handleConnect = async (appKey: string, appName: string) => {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return

    setConnectingApps(prev => new Set(prev).add(appKey))

    try {
      // Initiate connection
      const initResult = await ComposioMCPService.initiateConnection(appKey)

      // Store flags for post-OAuth handling
      localStorage.setItem('composio_recently_connected', appKey)
      localStorage.setItem('composio_connection_request_id', initResult.connection_request_id)
      localStorage.setItem('composio_connection_app_name', appName)

      // Use full page redirect for authentication
      window.location.href = initResult.redirect_url

    } catch (error: any) {
      console.error('Connection error:', error)
      toast.error(`Failed to connect to ${appName}`)
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev)
        newSet.delete(appKey)
        return newSet
      })
    }
  }

  // Handle MCP server disconnection
  const handleDisconnect = async (appKey: string, appName: string) => {
    if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return

    setDisconnectingApps(prev => new Set(prev).add(appKey))

    try {
      await ComposioMCPService.deleteConnection(appKey)
      
      setConnectedApps(prev => {
        const newSet = new Set(prev)
        newSet.delete(appKey)
        return newSet
      })

      // Invalidate agent queries to refresh MCP tools
      queryClient.invalidateQueries({ queryKey: agentKeys.all })
      
      toast.success(`Disconnected from ${appName}`)
    } catch (error: any) {
      console.error('Disconnection error:', error)
      toast.error(`Failed to disconnect from ${appName}`)
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev)
        newSet.delete(appKey)
        return newSet
      })
    }
  }

  const handleSelectPrompt = (query: string) => {
    const encodedPrompt = encodeURIComponent(query)
    router.push(`/dashboard?prompt=${encodedPrompt}`)
  }

  return (
    <>
      {/* Mobile Header */}
      {isMobile && (
        <div className="bg-background border-b border-border px-4 py-3 flex items-center justify-between md:hidden">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            <h1 className="text-lg font-semibold text-foreground">Prompts</h1>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setOpenMobile(!openMobile)}
              >
                {openMobile ? (
                  <X className="h-4 w-4" />
                ) : (
                  <Menu className="h-4 w-4" />
                )}
                <span className="sr-only">
                  {openMobile ? "Close menu" : "Open menu"}
                </span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {openMobile ? "Close menu" : "Open menu"}
            </TooltipContent>
          </Tooltip>
        </div>
      )}
      <div className="container mx-auto max-w-7xl px-4 py-8">
        <div className="space-y-8">
          <div className='w-full space-y-4 bg-gradient-to-b from-primary/10 to-primary/5 border rounded-xl h-60 flex items-center justify-center'>
            <div className="space-y-4">
              <div className="space-y-2 text-center">
                <div className='flex items-center justify-center gap-2'>
                  <FileText className='h-6 w-6 text-primary' />
                  <h1 className="text-2xl font-semibold tracking-tight text-foreground">
                    Prompts
                  </h1>
                </div>
                <p className="text-md text-muted-foreground max-w-2xl">
                  Discover and use pre-built prompts to get the most out of your AI workflows
                </p>
              </div>
            </div>
          </div>

          <AllPrompts 
            onSelectPrompt={handleSelectPrompt}
            onRequireIntegrations={handleRequireIntegrations}
          />
        </div>
      </div>

      <MCPAppsModal
        isOpen={isFilteredModalOpen}
        onClose={() => setIsFilteredModalOpen(false)}
        apps={apps.filter(app => filteredAppKeys.includes(app.key))}
        connectedApps={connectedApps}
        connectingApps={connectingApps}
        disconnectingApps={disconnectingApps}
        onConnect={handleConnect}
        onDisconnect={handleDisconnect}
        isLoading={loading}
        filterTitle={modalTitle}
        filterDescription={modalDescription}
      />
    </>
  )
}