"""
Composio v3 SDK Integration Service

This service implements the new Composio v3 authentication flow using the updated SDK.
It uses the user's Supabase UUID as the entity ID and pre-configured auth config IDs.

Flow:
1. Initialize Composio client with API key
2. Use user's Supabase UUID as entity ID
3. Initiate OAuth connection using pre-configured auth config IDs
4. Wait for connection establishment
5. Store connection in Supabase under agents as custom_mcp

New v3 Features:
- Clean SDK initialization with new Composio()
- composio.connected_accounts.initiate(entity_id, integration_id) for OAuth requests
- connRequest.wait_until_active() for connection establishment
- Uses user's Supabase UUID as entity ID
- Uses pre-configured auth config IDs from constants
"""

import uuid
import json
import os
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
from supabase import create_client, Client
from utils.logger import logger
from utils.default_agent_config import get_default_agent_config
import composio
from composio.client.exceptions import ComposioClientError

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

# Composio v3 configuration
COMPOSIO_API_KEY = os.getenv("COMPOSIO_API_KEY")


@dataclass
class ComposioV3Connection:
    """Result of Composio v3 connection creation"""

    success: bool
    app_key: str
    auth_config_id: Optional[str] = None
    connection_id: Optional[str] = None
    redirect_url: Optional[str] = None
    connection_request_id: Optional[str] = None
    entity_id: Optional[str] = None
    error: Optional[str] = None
    message: Optional[str] = None


@dataclass
class ComposioV3AuthResult:
    """Result of Composio v3 authentication completion"""

    success: bool
    app_key: str
    connection_id: Optional[str] = None
    connected_account_id: Optional[str] = None
    entity_id: Optional[str] = None
    toolsets: Optional[List[str]] = None
    error: Optional[str] = None


class ComposioV3Service:
    """Service for managing Composio v3 SDK integrations"""

    def __init__(self):
        if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
            raise ValueError("Supabase configuration missing")

        if not COMPOSIO_API_KEY:
            raise ValueError("Composio API key missing")

        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
        self.composio = None
        self._init_composio_client()

    def _init_composio_client(self):
        """Initialize Composio v3 client"""
        try:
            # Import Composio v3 SDK
            from composio import Composio

            self.composio = Composio(api_key=COMPOSIO_API_KEY)
            logger.info("Successfully initialized Composio v3 client")
        except ImportError:
            logger.error(
                "Composio v3 SDK not installed. Please install with: pip install composio-core"
            )
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Composio v3 client: {e}")
            raise

    def _load_integration_config(self, app_key: str) -> Optional[Dict[str, Any]]:
        """Load integration configuration from constants file"""
        try:
            constants_file = os.path.join(
                os.path.dirname(__file__),
                "..",
                "constants",
                "composio_mcp_servers.json",
            )

            with open(constants_file, "r") as f:
                constants = json.load(f)

            return constants.get(app_key)
        except Exception as e:
            logger.error(f"Failed to load integration config for {app_key}: {e}")
            return None

    async def initiate_connection(
        self, app_key: str, user_id: str
    ) -> ComposioV3Connection:
        """
        Initiate OAuth connection request using Composio v3 SDK.

        Uses the user's Supabase UUID as entity ID and pre-configured auth config IDs.

        Args:
            app_key: The app to connect (e.g., "gmail", "notion")
            user_id: The user's Supabase UUID (used as entity ID)

        Returns:
            ComposioV3Connection with redirect_url for OAuth flow
        """
        try:
            # Load integration configuration
            integration_config = self._load_integration_config(app_key)
            if not integration_config:
                return ComposioV3Connection(
                    success=False,
                    app_key=app_key,
                    error=f"Integration configuration not found for {app_key}",
                )

            # Get pre-configured auth config ID
            auth_config_id = integration_config.get("integration_id")
            if not auth_config_id:
                return ComposioV3Connection(
                    success=False,
                    app_key=app_key,
                    error=f"Auth config ID not found for {app_key}",
                )

            logger.info(
                f"Initiating connection for {app_key} with auth config {auth_config_id} and entity {user_id}"
            )

            # Initiate connection using v3 SDK with user's Supabase UUID as entity ID
            conn_request = self.composio.connected_accounts.initiate(
                entity_id=user_id,  # User's Supabase UUID as entity ID
                integration_id=auth_config_id,  # Pre-configured auth config ID
                redirect_url="https://atlasagents.ai/dashboard",  # Post-auth redirect destination
            )

            logger.info(
                f"Successfully initiated connection request for {app_key}: {conn_request.connectedAccountId}"
            )

            return ComposioV3Connection(
                success=True,
                app_key=app_key,
                auth_config_id=auth_config_id,
                connection_request_id=conn_request.connectedAccountId,
                redirect_url=conn_request.redirectUrl,
                entity_id=user_id,
                message=f"Connection request initiated for {app_key}. Please complete authentication at the provided URL.",
            )

        except Exception as e:
            logger.error(f"Failed to initiate connection for {app_key}: {e}")
            return ComposioV3Connection(success=False, app_key=app_key, error=str(e))

    async def wait_for_connection(
        self, connection_request_id: str, app_key: str, user_id: str, timeout: int = 300
    ) -> ComposioV3AuthResult:
        """
        Wait for connection establishment and return connection details.

        Args:
            connection_request_id: The connection request ID from initiate_connection
            app_key: The app key (e.g., "gmail", "notion")
            user_id: The user's Supabase UUID
            timeout: Timeout in seconds (default: 300)

        Returns:
            ComposioV3AuthResult with connection details
        """
        try:
            logger.info(f"Waiting for connection completion: {connection_request_id}")

            # The connection_request_id is actually the connectedAccountId from initiate()
            # We need to check the connection status directly
            # Since wait_until_active is only available on ConnectionRequestModel,
            # we'll poll the connected account status instead

            import time

            start_time = time.time()

            while time.time() - start_time < timeout:
                # Get the connected account by ID
                try:
                    connection = self.composio.connected_accounts.get(
                        connection_request_id
                    )

                    # Check if connection is active/established
                    if hasattr(connection, "status") and connection.status == "ACTIVE":
                        logger.info(f"Connection is now active: {connection.id}")
                        break

                    # Wait a bit before checking again
                    await asyncio.sleep(5)

                except Exception as poll_error:
                    logger.warning(f"Error polling connection status: {poll_error}")
                    await asyncio.sleep(5)
            else:
                # Timeout reached
                logger.warning(
                    f"Connection establishment timed out after {timeout} seconds"
                )
                connection = None

            if connection and connection.id:
                logger.info(f"Connection established for {app_key}: {connection.id}")

                # Get available toolsets for this connection
                toolsets = await self._get_connection_toolsets(connection.id, app_key)

                # Store the connection in Supabase
                stored = await self._store_connection_in_supabase(
                    user_id=user_id,
                    app_key=app_key,
                    connection_id=connection.id,
                    connected_account_id=getattr(
                        connection, "connectedAccountId", connection.id
                    ),
                    entity_id=user_id,
                    toolsets=toolsets,
                )

                if stored:
                    return ComposioV3AuthResult(
                        success=True,
                        app_key=app_key,
                        connection_id=connection.id,
                        connected_account_id=getattr(
                            connection, "connectedAccountId", connection.id
                        ),
                        entity_id=user_id,
                        toolsets=toolsets,
                    )
                else:
                    return ComposioV3AuthResult(
                        success=False,
                        app_key=app_key,
                        entity_id=user_id,
                        error="Failed to store connection in database",
                    )
            else:
                return ComposioV3AuthResult(
                    success=False,
                    app_key=app_key,
                    entity_id=user_id,
                    error="Connection not established within timeout period",
                )

        except Exception as e:
            logger.error(f"Failed to wait for connection {connection_request_id}: {e}")
            return ComposioV3AuthResult(
                success=False, app_key=app_key, entity_id=user_id, error=str(e)
            )

    async def _get_connection_toolsets(
        self, connection_id: str, app_key: str
    ) -> List[str]:
        """Get available toolsets for a connection from Composio SDK"""
        try:
            # Try to get actual tools from Composio SDK
            try:
                # Get the connected account and fetch available actions
                connection = self.composio.connected_accounts.get(connection_id)
                if connection:
                    # Get available actions for this app/integration
                    actions = self.composio.actions.list(
                        tags=[app_key.upper()], enabled_for_account=connection_id
                    )

                    tool_names = []
                    for action in actions:
                        # Extract action name/identifier
                        if hasattr(action, "name"):
                            tool_names.append(action.name)
                        elif hasattr(action, "id"):
                            tool_names.append(action.id)
                        elif hasattr(action, "action_name"):
                            tool_names.append(action.action_name)

                    if tool_names:
                        logger.info(
                            f"Found {len(tool_names)} tools for {app_key} from Composio SDK"
                        )
                        return tool_names

            except Exception as sdk_error:
                logger.warning(f"Failed to get tools from Composio SDK: {sdk_error}")

            # Fallback to tools from JSON configuration
            integration_config = self._load_integration_config(app_key)
            if integration_config and "tools" in integration_config:
                tools = [tool["name"] for tool in integration_config["tools"]]
                logger.info(f"Using {len(tools)} tools from JSON config for {app_key}")
                return tools

            # If no tools found, return empty list
            logger.warning(f"No tools found for {app_key}")
            return []

        except Exception as e:
            logger.error(f"Failed to get toolsets for connection {connection_id}: {e}")
            return []

    async def _store_connection_in_supabase(
        self,
        user_id: str,
        app_key: str,
        connection_id: str,
        connected_account_id: str,
        entity_id: str,
        toolsets: List[str],
    ) -> bool:
        """Store the v3 connection in Supabase under agents as custom_mcp"""
        try:
            # Get or create default agent for user
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                logger.error(f"Failed to get/create default agent for user {user_id}")
                return False

            # Load integration config
            integration_config = self._load_integration_config(app_key)
            if not integration_config:
                logger.error(f"Integration config not found for {app_key}")
                return False

            # Create custom MCP entry for the v3 connection in the proper MCP server format
            # URL format: https://mcp.composio.dev/composio/server/{server_id}/mcp?user_id={entity_id}
            server_id = integration_config.get("server_id")
            mcp_url = f"https://mcp.composio.dev/composio/server/{server_id}/mcp?user_id={entity_id}"

            custom_mcp_entry = {
                "name": integration_config.get("name", app_key.title()),
                "type": "http",
                "config": {"url": mcp_url},
                "enabledTools": toolsets,
                # Include app_key for deletion logic compatibility
                "app_key": app_key,
                # Store additional metadata for potential future use (hidden from MCP)
                "_metadata": {
                    "auth_type": "composio_v3",
                    "connection_id": connection_id,
                    "connected_account_id": connected_account_id,
                    "entity_id": entity_id,
                    "server_id": server_id,
                    "auth_config_id": integration_config.get("integration_id"),
                    "qualified_name": f"composio/{app_key}",
                    "description": integration_config.get(
                        "description", f"{app_key.title()} integration"
                    ),
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                },
            }

            # Get existing custom MCPs
            existing_mcps = agent.get("custom_mcps", [])
            if not isinstance(existing_mcps, list):
                existing_mcps = []

            # Remove existing connection for same app_key if exists
            existing_mcps = [
                mcp for mcp in existing_mcps if mcp.get("app_key") != app_key
            ]

            # Add new connection
            existing_mcps.append(custom_mcp_entry)

            # Update agent with new custom MCPs
            result = (
                self.supabase.table("agents")
                .update(
                    {
                        "custom_mcps": existing_mcps,
                    }
                )
                .eq("agent_id", agent["agent_id"])
                .execute()
            )

            if result.data:
                logger.info(
                    f"Successfully stored v3 connection for {app_key} with entity ID {entity_id}"
                )
                return True
            else:
                logger.error(f"Failed to update agent with v3 connection for {app_key}")
                return False

        except Exception as e:
            logger.error(f"Failed to store v3 connection in Supabase: {e}")
            return False

    async def _get_or_create_default_agent(
        self, user_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get or create default agent for user"""
        try:
            # First try to get existing default agent
            # Note: agents table uses account_id (same as user's Supabase UUID)
            result = (
                self.supabase.table("agents")
                .select("*")
                .eq("account_id", user_id)
                .eq("is_default", True)
                .execute()
            )

            if result.data:
                return result.data[0]

            # If no default agent exists, create one using the standard configuration
            agent_data = get_default_agent_config(user_id)

            result = self.supabase.table("agents").insert(agent_data).execute()

            if result.data:
                logger.info(f"Created default agent for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to create default agent for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Failed to get/create default agent for user {user_id}: {e}")
            return None

    async def list_user_connections(self, user_id: str) -> List[Dict[str, Any]]:
        """List all Composio v3 connections for a user"""
        try:
            # Get user's default agent
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                return []

            # Filter custom MCPs for Composio v3 connections
            custom_mcps = agent.get("custom_mcps", [])
            if not isinstance(custom_mcps, list):
                return []

            v3_connections = [
                mcp
                for mcp in custom_mcps
                if (
                    mcp.get("auth_type") == "composio_v3"
                    or mcp.get("_metadata", {}).get("auth_type") == "composio_v3"
                )
            ]

            logger.info(
                f"Found {len(v3_connections)} v3 connections for user {user_id}"
            )
            return v3_connections

        except Exception as e:
            logger.error(f"Failed to list user v3 connections: {e}")
            return []

    async def delete_user_connection(self, user_id: str, app_key: str) -> bool:
        """Delete a Composio v3 connection for a user"""
        try:
            # Get user's default agent
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                return False

            # Remove the connection from custom MCPs
            custom_mcps = agent.get("custom_mcps", [])
            if not isinstance(custom_mcps, list):
                custom_mcps = []

            updated_mcps = [mcp for mcp in custom_mcps if mcp.get("app_key") != app_key]

            # Update agent
            result = (
                self.supabase.table("agents")
                .update(
                    {
                        "custom_mcps": updated_mcps,
                    }
                )
                .eq("agent_id", agent["agent_id"])
                .execute()
            )

            if result.data:
                logger.info(f"Successfully deleted v3 connection for {app_key}")
                return True
            else:
                logger.error(f"Failed to delete v3 connection for {app_key}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete user v3 connection: {e}")
            return False

    async def get_supported_integrations(self) -> List[Dict[str, Any]]:
        """Get list of supported integrations from constants file"""
        try:
            constants_file = os.path.join(
                os.path.dirname(__file__),
                "..",
                "constants",
                "composio_mcp_servers.json",
            )

            with open(constants_file, "r") as f:
                constants = json.load(f)

            integrations = []
            for app_key, config in constants.items():
                integrations.append(
                    {
                        "app_key": app_key,
                        "name": config.get("name", app_key.title()),
                        "description": config.get(
                            "description", f"{app_key.title()} integration"
                        ),
                        "auth_config_id": config.get("integration_id"),
                        "server_id": config.get("server_id"),
                    }
                )

            return integrations

        except Exception as e:
            logger.error(f"Failed to get supported integrations: {e}")
            return []


# Global service instance
composio_v3_service = ComposioV3Service()
