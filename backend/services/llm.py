"""
LLM API interface for making calls to various language models.

This module provides a unified interface for making API calls to different LLM providers
(OpenAI, Anthropic, Groq, etc.) using LiteLLM. It includes support for:
- Streaming responses
- Tool calls and function calling
- Retry logic with exponential backoff for different error types
- Model-specific configurations
- Comprehensive error handling and logging
- Special handling for server overload scenarios (AnthropicException - Overloaded)

Error Handling:
- Rate limit errors: 30 second delay between retries
- Server overload errors: 60+ second delay with exponential backoff
- General errors: 0.1 second delay between retries
- Maximum 3 retry attempts by default (configurable)
"""

from typing import Union, Dict, Any, Optional, AsyncGenerator, List
import os
import json
import asyncio
from openai import OpenAIError
import litellm
from utils.logger import logger
from utils.config import config

# litellm.set_verbose=True
litellm.modify_params = True

# Constants - use config values with fallbacks
MAX_RETRIES = config.LLM_MAX_RETRIES
RATE_LIMIT_DELAY = config.LLM_RATE_LIMIT_DELAY
RETRY_DELAY = config.LLM_RETRY_DELAY
OVERLOAD_DELAY = config.LLM_OVERLOAD_DELAY


class LLMError(Exception):
    """Base exception for LLM-related errors."""

    pass


class LLMRetryError(LLMError):
    """Exception raised when retries are exhausted."""

    def __init__(self, message: str):
        super().__init__(message)
        self.technical_details = None
        self.last_error = None


def setup_api_keys() -> None:
    """Set up API keys from environment variables."""
    providers = ["OPENAI", "ANTHROPIC", "GROQ", "OPENROUTER"]
    for provider in providers:
        key = getattr(config, f"{provider}_API_KEY")
        if key:
            # Set the environment variable for LiteLLM to use
            os.environ[f"{provider}_API_KEY"] = key
            logger.debug(f"API key set for provider: {provider}")
        else:
            logger.warning(f"No API key found for provider: {provider}")

    # Set up OpenRouter API base if not already set
    if config.OPENROUTER_API_KEY and config.OPENROUTER_API_BASE:
        os.environ["OPENROUTER_API_BASE"] = config.OPENROUTER_API_BASE
        logger.debug(f"Set OPENROUTER_API_BASE to {config.OPENROUTER_API_BASE}")

    # Set up AWS Bedrock credentials
    aws_access_key = config.AWS_ACCESS_KEY_ID
    aws_secret_key = config.AWS_SECRET_ACCESS_KEY
    aws_region = config.AWS_REGION_NAME

    if aws_access_key and aws_secret_key and aws_region:
        logger.debug(f"AWS credentials set for Bedrock in region: {aws_region}")
        # Configure LiteLLM to use AWS credentials
        os.environ["AWS_ACCESS_KEY_ID"] = aws_access_key
        os.environ["AWS_SECRET_ACCESS_KEY"] = aws_secret_key
        os.environ["AWS_REGION_NAME"] = aws_region
    else:
        logger.warning(
            f"Missing AWS credentials for Bedrock integration - access_key: {bool(aws_access_key)}, secret_key: {bool(aws_secret_key)}, region: {aws_region}"
        )


def get_fallback_model(original_model: str) -> Optional[str]:
    """Get a fallback model when the original is overloaded."""
    original_lower = original_model.lower()

    # Anthropic models -> OpenAI fallbacks
    if "claude" in original_lower or "anthropic" in original_lower:
        if "sonnet" in original_lower:
            return "openai/gpt-4o"
        elif "haiku" in original_lower:
            return "openai/gpt-4o-mini"
        else:
            return "openai/gpt-4o"

    # OpenAI models -> Anthropic fallbacks
    elif "gpt-4" in original_lower or "openai" in original_lower:
        if "mini" in original_lower:
            return "anthropic/claude-3-haiku-20240307"
        else:
            return "anthropic/claude-3-5-sonnet-20241022"

    # For other models, default to a reliable option
    return "openai/gpt-4o-mini"


def is_overload_error(error: Exception) -> bool:
    """Check if the error indicates server overload."""
    error_str = str(error).lower()
    return (
        "overloaded" in error_str
        or "overload" in error_str
        or "server overloaded" in error_str
        or "temporarily unavailable" in error_str
        or "service unavailable" in error_str
        or "anthropicexception - overloaded" in error_str
        or "anthropicexception: overloaded" in error_str
        or "handle with `litellm.internalservererror`" in error_str
        or "502 bad gateway" in error_str
        or "503 service unavailable" in error_str
        or "504 gateway timeout" in error_str
        or "server is overloaded" in error_str
        or "too many requests" in error_str
        or "badgateway" in error_str
        or "bad gateway" in error_str
        or "litellm has no property" in error_str
        or ("capacity" in error_str and "exceeded" in error_str)
    )


async def handle_error(error: Exception, attempt: int, max_attempts: int) -> None:
    """Handle API errors with appropriate delays and logging."""
    if isinstance(error, litellm.exceptions.RateLimitError):
        delay = RATE_LIMIT_DELAY
        error_type = "Rate Limit"
    elif is_overload_error(error):
        # More aggressive exponential backoff for overload: 45, 90, 180, 360, 720 seconds
        delay = OVERLOAD_DELAY * (2**attempt)
        error_type = "Server Overload"
    else:
        delay = RETRY_DELAY
        error_type = "General"

    logger.warning(
        f"{error_type} error on attempt {attempt + 1}/{max_attempts}: {str(error)}"
    )
    logger.info(
        f"Waiting {delay} seconds before retry (attempt {attempt + 1}/{max_attempts})..."
    )
    await asyncio.sleep(delay)


def prepare_params(
    messages: List[Dict[str, Any]],
    model_name: str,
    temperature: float = 0,
    max_tokens: Optional[int] = None,
    response_format: Optional[Any] = None,
    tools: Optional[List[Dict[str, Any]]] = None,
    tool_choice: str = "auto",
    api_key: Optional[str] = None,
    api_base: Optional[str] = None,
    stream: bool = False,
    top_p: Optional[float] = None,
    model_id: Optional[str] = None,
    enable_thinking: Optional[bool] = False,
    reasoning_effort: Optional[str] = "low",
) -> Dict[str, Any]:
    """Prepare parameters for the API call."""
    # OpenAI O3 models only support temperature=1
    if "o3" in model_name:
        temperature = 1.0

    params = {
        "model": model_name,
        "messages": messages,
        "temperature": temperature,
        "response_format": response_format,
        "top_p": top_p,
        "stream": stream,
    }

    if api_key:
        params["api_key"] = api_key
    if api_base:
        params["api_base"] = api_base
    if model_id:
        params["model_id"] = model_id

    # Handle token limits
    if max_tokens is not None:
        # For Claude 3.7 in Bedrock, do not set max_tokens or max_tokens_to_sample
        # as it causes errors with inference profiles
        if model_name.startswith("bedrock/") and "claude-3-7" in model_name:
            logger.debug(f"Skipping max_tokens for Claude 3.7 model: {model_name}")
            # Do not add any max_tokens parameter for Claude 3.7
        else:
            # OpenAI O1 and O3 models use max_completion_tokens instead of max_tokens
            param_name = (
                "max_completion_tokens"
                if ("o1" in model_name or "o3" in model_name)
                else "max_tokens"
            )
            params[param_name] = max_tokens

    # Add tools if provided
    if tools:
        params.update({"tools": tools, "tool_choice": tool_choice})
        logger.debug(f"Added {len(tools)} tools to API parameters")

    # # Add Claude-specific headers
    if "claude" in model_name.lower() or "anthropic" in model_name.lower():
        params["extra_headers"] = {
            # "anthropic-beta": "max-tokens-3-5-sonnet-2024-07-15"
            "anthropic-beta": "output-128k-2025-02-19"
        }
        params["fallbacks"] = [
            {
                "model": "openrouter/anthropic/claude-sonnet-4",
                "messages": messages,
            }
        ]
        # params["mock_testing_fallback"] = True
        logger.debug("Added Claude-specific headers")

    # Add OpenRouter-specific parameters
    if model_name.startswith("openrouter/"):
        logger.debug(f"Preparing OpenRouter parameters for model: {model_name}")

        # Add optional site URL and app name from config
        site_url = config.OR_SITE_URL
        app_name = config.OR_APP_NAME
        if site_url or app_name:
            extra_headers = params.get("extra_headers", {})
            if site_url:
                extra_headers["HTTP-Referer"] = site_url
            if app_name:
                extra_headers["X-Title"] = app_name
            params["extra_headers"] = extra_headers
            logger.debug(f"Added OpenRouter site URL and app name to headers")

    # Add Bedrock-specific parameters
    if model_name.startswith("bedrock/"):
        logger.debug(f"Preparing AWS Bedrock parameters for model: {model_name}")

        if not model_id and "anthropic.claude-3-7-sonnet" in model_name:
            params["model_id"] = (
                "arn:aws:bedrock:us-west-2:935064898258:inference-profile/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
            )
            logger.debug(
                f"Auto-set model_id for Claude 3.7 Sonnet: {params['model_id']}"
            )

    # Apply Anthropic prompt caching (minimal implementation)
    # Check model name *after* potential modifications (like adding bedrock/ prefix)
    effective_model_name = params.get(
        "model", model_name
    )  # Use model from params if set, else original
    if (
        "claude" in effective_model_name.lower()
        or "anthropic" in effective_model_name.lower()
    ):
        messages = params["messages"]  # Direct reference, modification affects params

        # Ensure messages is a list
        if not isinstance(messages, list):
            return params  # Return early if messages format is unexpected

        # Apply cache control to the first 4 text blocks across all messages
        cache_control_count = 0
        max_cache_control_blocks = 4

        for message in messages:
            if cache_control_count >= max_cache_control_blocks:
                break

            content = message.get("content")

            if isinstance(content, str):
                message["content"] = [
                    {
                        "type": "text",
                        "text": content,
                        "cache_control": {"type": "ephemeral"},
                    }
                ]
                cache_control_count += 1
            elif isinstance(content, list):
                for item in content:
                    if cache_control_count >= max_cache_control_blocks:
                        break
                    if (
                        isinstance(item, dict)
                        and item.get("type") == "text"
                        and "cache_control" not in item
                    ):
                        item["cache_control"] = {"type": "ephemeral"}
                        cache_control_count += 1

    # Add reasoning_effort for Anthropic models if enabled
    use_thinking = enable_thinking if enable_thinking is not None else False
    is_anthropic = (
        "anthropic" in effective_model_name.lower()
        or "claude" in effective_model_name.lower()
    )

    if is_anthropic and use_thinking:
        effort_level = reasoning_effort if reasoning_effort else "low"
        params["reasoning_effort"] = effort_level
        params["temperature"] = (
            1.0  # Required by Anthropic when reasoning_effort is used
        )
        logger.info(
            f"Anthropic thinking enabled with reasoning_effort='{effort_level}'"
        )

    return params


async def make_llm_api_call(
    messages: List[Dict[str, Any]],
    model_name: str,
    response_format: Optional[Any] = None,
    temperature: float = 0,
    max_tokens: Optional[int] = None,
    tools: Optional[List[Dict[str, Any]]] = None,
    tool_choice: str = "auto",
    api_key: Optional[str] = None,
    api_base: Optional[str] = None,
    stream: bool = False,
    top_p: Optional[float] = None,
    model_id: Optional[str] = None,
    enable_thinking: Optional[bool] = False,
    reasoning_effort: Optional[str] = "low",
) -> Union[Dict[str, Any], AsyncGenerator]:
    """
    Make an API call to a language model using LiteLLM.

    Args:
        messages: List of message dictionaries for the conversation
        model_name: Name of the model to use (e.g., "gpt-4", "claude-3", "openrouter/openai/gpt-4", "bedrock/anthropic.claude-3-sonnet-20240229-v1:0")
        response_format: Desired format for the response
        temperature: Sampling temperature (0-1)
        max_tokens: Maximum tokens in the response
        tools: List of tool definitions for function calling
        tool_choice: How to select tools ("auto" or "none")
        api_key: Override default API key
        api_base: Override default API base URL
        stream: Whether to stream the response
        top_p: Top-p sampling parameter
        model_id: Optional ARN for Bedrock inference profiles
        enable_thinking: Whether to enable thinking
        reasoning_effort: Level of reasoning effort

    Returns:
        Union[Dict[str, Any], AsyncGenerator]: API response or stream

    Raises:
        LLMRetryError: If API call fails after retries
        LLMError: For other API-related errors
    """
    # debug <timestamp>.json messages
    logger.info(
        f"Making LLM API call to model: {model_name} (Thinking: {enable_thinking}, Effort: {reasoning_effort})"
    )
    logger.info(f"📡 API Call: Using model {model_name}")
    params = prepare_params(
        messages=messages,
        model_name=model_name,
        temperature=temperature,
        max_tokens=max_tokens,
        response_format=response_format,
        tools=tools,
        tool_choice=tool_choice,
        api_key=api_key,
        api_base=api_base,
        stream=stream,
        top_p=top_p,
        model_id=model_id,
        enable_thinking=enable_thinking,
        reasoning_effort=reasoning_effort,
    )
    last_error = None
    for attempt in range(MAX_RETRIES):
        try:
            logger.debug(f"Attempt {attempt + 1}/{MAX_RETRIES}")
            # logger.debug(f"API request parameters: {json.dumps(params, indent=2)}")

            response = await litellm.acompletion(**params)
            logger.debug(f"Successfully received API response from {model_name}")
            # logger.debug(f"Response: {response}")
            return response

        except (
            litellm.exceptions.RateLimitError,
            OpenAIError,
            json.JSONDecodeError,
        ) as e:
            last_error = e
            await handle_error(e, attempt, MAX_RETRIES)

        except (
            litellm.InternalServerError,
            litellm.ServiceUnavailableError,
            getattr(litellm, "BadGatewayError", Exception),
        ) as e:
            last_error = e
            # Check if this is an overload error that should be retried
            if is_overload_error(e) and attempt < MAX_RETRIES - 1:
                logger.warning(
                    f"Server overload/unavailable detected, retrying: {str(e)}"
                )
                await handle_error(e, attempt, MAX_RETRIES)
            else:
                # If not an overload error or we've exhausted retries, fail immediately
                logger.error(f"Server error during API call: {str(e)}", exc_info=True)
                raise LLMError(f"API call failed with server error: {str(e)}")

        except AttributeError as e:
            # Handle cases where litellm properties/methods don't exist
            last_error = e
            error_msg = f"LiteLLM compatibility error: {str(e)}"
            logger.error(f"AttributeError during API call: {error_msg}", exc_info=True)

            # Treat as a server error and retry if we haven't exhausted attempts
            if attempt < MAX_RETRIES - 1:
                logger.warning(f"Retrying due to AttributeError: {error_msg}")
                await handle_error(e, attempt, MAX_RETRIES)
            else:
                raise LLMError(
                    f"API call failed due to compatibility issue: {error_msg}"
                )

        except Exception as e:
            last_error = e
            error_msg = f"Unexpected error during API call: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # Check if this looks like a network/gateway error that should be retried
            error_str = str(e).lower()
            if (
                "gateway" in error_str
                or "timeout" in error_str
                or "connection" in error_str
            ) and attempt < MAX_RETRIES - 1:
                logger.warning(f"Retrying due to potential network error: {error_msg}")
                await handle_error(e, attempt, MAX_RETRIES)
            else:
                raise LLMError(f"API call failed: {str(e)}")

    # Try fallback model if original failed due to overload
    if last_error and is_overload_error(last_error) and config.LLM_ENABLE_FALLBACK:
        fallback_model = get_fallback_model(model_name)
        if fallback_model and fallback_model != model_name:
            logger.warning(
                f"Attempting fallback to {fallback_model} due to {model_name} overload"
            )
            try:
                # Update params with fallback model
                fallback_params = params.copy()
                fallback_params["model"] = fallback_model

                response = await litellm.acompletion(**fallback_params)
                logger.info(f"Successfully used fallback model {fallback_model}")
                return response
            except Exception as fallback_error:
                logger.error(
                    f"Fallback model {fallback_model} also failed: {str(fallback_error)}"
                )

    # Create a user-friendly error message
    if last_error and is_overload_error(last_error):
        user_friendly_msg = f"The AI service is currently experiencing high demand. Please try again in a few minutes."
        technical_msg = f"Failed to make API call after {MAX_RETRIES} attempts due to server overload. Last error: {str(last_error)}"
    else:
        user_friendly_msg = f"Unable to connect to AI service after {MAX_RETRIES} attempts. Please try again."
        technical_msg = f"Failed to make API call after {MAX_RETRIES} attempts. Last error: {str(last_error) if last_error else 'Unknown'}"

    logger.error(technical_msg, exc_info=True)

    # Raise with user-friendly message but include technical details
    error = LLMRetryError(user_friendly_msg)
    error.technical_details = technical_msg
    error.last_error = last_error
    raise error


# Initialize API keys on module import
setup_api_keys()


# Test code for OpenRouter integration
async def test_openrouter():
    """Test the OpenRouter integration with a simple query."""
    test_messages = [
        {"role": "user", "content": "Hello, can you give me a quick test response?"}
    ]

    try:
        # Test with standard OpenRouter model
        print("\n--- Testing standard OpenRouter model ---")
        response = await make_llm_api_call(
            model_name="openrouter/openai/gpt-4o-mini",
            messages=test_messages,
            temperature=0.7,
            max_tokens=100,
        )
        print(f"Response: {response.choices[0].message.content}")

        # Test with deepseek model
        print("\n--- Testing deepseek model ---")
        response = await make_llm_api_call(
            model_name="openrouter/deepseek/deepseek-r1-distill-llama-70b",
            messages=test_messages,
            temperature=0.7,
            max_tokens=100,
        )
        print(f"Response: {response.choices[0].message.content}")
        print(f"Model used: {response.model}")

        # Test with Mistral model
        print("\n--- Testing Mistral model ---")
        response = await make_llm_api_call(
            model_name="openrouter/mistralai/mixtral-8x7b-instruct",
            messages=test_messages,
            temperature=0.7,
            max_tokens=100,
        )
        print(f"Response: {response.choices[0].message.content}")
        print(f"Model used: {response.model}")

        return True
    except Exception as e:
        print(f"Error testing OpenRouter: {str(e)}")
        return False


async def test_bedrock():
    """Test the AWS Bedrock integration with a simple query."""
    test_messages = [
        {"role": "user", "content": "Hello, can you give me a quick test response?"}
    ]

    try:
        response = await make_llm_api_call(
            model_name="bedrock/anthropic.claude-3-7-sonnet-20250219-v1:0",
            model_id="arn:aws:bedrock:us-west-2:935064898258:inference-profile/us.anthropic.claude-3-7-sonnet-20250219-v1:0",
            messages=test_messages,
            temperature=0.7,
            # Claude 3.7 has issues with max_tokens, so omit it
            # max_tokens=100
        )
        print(f"Response: {response.choices[0].message.content}")
        print(f"Model used: {response.model}")

        return True
    except Exception as e:
        print(f"Error testing Bedrock: {str(e)}")
        return False


if __name__ == "__main__":
    import asyncio

    test_success = asyncio.run(test_bedrock())

    if test_success:
        print("\n✅ integration test completed successfully!")
    else:
        print("\n❌ Bedrock integration test failed!")
