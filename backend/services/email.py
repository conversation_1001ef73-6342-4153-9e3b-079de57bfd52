import os
import logging
from typing import Optional
from sendgrid import SendGridAPIClient
from utils.config import config

logger = logging.getLogger(__name__)


class EmailService:
    def __init__(self):
        self.api_key = os.getenv("SENDGRID_API_KEY")

        if not self.api_key:
            logger.warning("SENDGRID_API_KEY not found in environment variables")
            self.client = None
        else:
            self.client = SendGridAPIClient(self.api_key)

    def send_welcome_email(
        self, user_email: str, user_name: Optional[str] = None
    ) -> bool:
        """Add user to SendGrid marketing contact list to trigger welcome email automation"""
        if not self.client:
            logger.error("Cannot add contact: SENDGRID_API_KEY not configured")
            return False

        try:
            # Prepare contact data
            contact_data = {"contacts": [{"email": user_email}]}
            # Add name if provided
            if user_name:
                contact_data["contacts"][0]["first_name"] = user_name

            # Add contact to SendGrid marketing list
            response = self.client.client.marketing.contacts.put(
                request_body=contact_data
            )

            if response.status_code in [200, 202]:
                logger.info(
                    f"Contact {user_email} added to SendGrid marketing list. Status: {response.status_code}"
                )
                return True
            else:
                logger.error(
                    f"Failed to add contact {user_email}. Status: {response.status_code}, Body: {response.body}"
                )
                return False

        except Exception as e:
            logger.error(f"Error adding contact {user_email} to SendGrid: {str(e)}")
            return False


email_service = EmailService()
